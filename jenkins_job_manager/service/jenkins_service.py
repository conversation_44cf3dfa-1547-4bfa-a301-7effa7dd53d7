import hashlib
import json
import time
from pathlib import Path
from typing import Dict, List, Optional

import yaml

from jenkins_job_manager.client.jenkins_client import Jenkins<PERSON>lient, JobStatus
from jenkins_job_manager.config.jjm_config import load_config
from jenkins_job_manager.config.config_manager import ConfigManager
from jenkins_job_manager.service.job_tracker import JobTracker


class JenkinsService:
    """Simple Jenkins job management service."""

    def __init__(self, jenkins_url: Optional[str] = None, config_dir: str = "conf"):
        """Initialize Jenkins service.

        Args:
            jenkins_url: Jenkins server URL (if None, loads from config)
            config_dir: Directory containing config files
        """
        self.config_dir = Path(config_dir)

        # Get Jenkins URL and job names from config
        config = load_config()
        self.jenkins_url = jenkins_url or config.get(
            'jenkins_url', 'http://localhost:8080')
        self.dataset_job, self.training_job = config.get(
            'dataset_job', 'dataset-creation-job'), config.get('training_job', 'training-pipeline-job')

        # Initialize components
        self.client = JenkinsClient(self.jenkins_url)
        self.config_manager = ConfigManager(self.config_dir)
        self.job_tracker = JobTracker(self.config_dir)

        # State tracking (legacy support)
        self.state_file = self.config_dir / 'jjm_state.json'

    def _load_state(self) -> Dict:
        """Load state from file."""
        if self.state_file.exists():
            with self.state_file.open('r') as f:
                return json.load(f)
        return {}

    def _save_state(self, state: Dict) -> None:
        """Save state to file."""
        with self.state_file.open('w') as f:
            json.dump(state, f, indent=2)

    def _get_config_hash(self, config_files: List[str]) -> str:
        """Get hash of config files for duplicate detection."""
        return self.config_manager.get_config_hash(config_files)

    def _is_default_config(self, config_file: str) -> bool:
        """Check if config file is still using default values."""
        state = self._load_state()
        defaults = state.get('defaults', {})

        file_path = self.config_dir / f"{config_file}_config.yml"
        if not file_path.exists():
            return True

        with file_path.open('r') as f:
            current_hash = hashlib.sha256(f.read().encode()).hexdigest()

        return current_hash == defaults.get(config_file)

    def _check_execution_state(self, config_files: List[str]) -> tuple[str, bool, List[str]]:
        """Check execution state and return hash, duplicate status, and default configs."""
        config_hash = self._get_config_hash(config_files)
        default_configs = [
            cf for cf in config_files if self._is_default_config(cf)]

        state = self._load_state()
        executions = state.get('executions', {})
        is_duplicate = config_hash in executions

        return config_hash, is_duplicate, default_configs

    def _record_execution(self, config_hash: str) -> None:
        """Record execution in state."""
        state = self._load_state()
        executions = state.setdefault('executions', {})

        if config_hash in executions:
            executions[config_hash]['count'] += 1
        else:
            executions[config_hash] = {'count': 1, 'first_run': time.time()}

        executions[config_hash]['last_run'] = time.time()
        self._save_state(state)

    def _merge_configs(self, config_files: List[str]) -> Dict:
        """Merge configuration files."""
        return self.config_manager.merge_configs(config_files)

    def _write_merged_config(self, merged_config: Dict, target_path: str) -> None:
        """Write merged config to target path."""
        self.config_manager.write_config(merged_config, target_path)

    def _prompt_approval(self, config_hash: str, is_duplicate: bool, default_configs: List[str], dry_run: bool = False) -> bool:
        """Prompt user for approval if needed."""
        if dry_run:
            print(f"DRY RUN: Would execute with config hash {config_hash}")
            if default_configs:
                print(
                    f"⚠️  Using default configs: {', '.join(default_configs)}")
            if is_duplicate:
                print("🔄 This configuration has been executed before")
            return True

        if default_configs or is_duplicate:
            print(f"\n📋 Configuration Summary:")
            print(f"Config hash: {config_hash}")

            if default_configs:
                print(
                    f"⚠️  Using default configs: {', '.join(default_configs)}")

            if is_duplicate:
                print("🔄 This configuration has been executed before")

            response = input("\nProceed? [y/N]: ").strip().lower()
            return response in ['y', 'yes']

        return True

    def execute_dataset_job(self, target_config_path: str = "/tmp/parameters.yml", dry_run: bool = False) -> Optional[str]:
        """Execute dataset creation job.

        Args:
            target_config_path: Path where merged config will be written
            dry_run: If True, only validate without executing

        Returns:
            Job ID if successful, None otherwise
        """
        config_files = ['query', 'sp']
        config_hash, is_duplicate, default_configs = self._check_execution_state(
            config_files)

        # Check approval
        if not self._prompt_approval(config_hash, is_duplicate, default_configs, dry_run):
            print("❌ Execution cancelled by user")
            return None

        if dry_run:
            print("DRY RUN: Would create dataset job")
            return "dry-run-dataset"

        # Merge configs and write to target
        merged_config = self._merge_configs(config_files)
        self._write_merged_config(merged_config, target_config_path)

        # Trigger Jenkins job
        build_number = self.client.trigger_job(
            self.dataset_job, {"config_path": target_config_path})

        if build_number is not None:
            # Create tracked job
            job_id = self.job_tracker.create_job(
                job_name=self.dataset_job,
                build_number=build_number,
                config_hash=config_hash,
                job_type='dataset'
            )

            self._record_execution(config_hash)
            print(
                f"✅ Dataset job '{self.dataset_job}' triggered successfully (Job ID: {job_id})")
            return job_id
        else:
            print(f"❌ Failed to trigger dataset job '{self.dataset_job}'")
            return None

    def execute_training_job(self, dry_run: bool = False, parent_job_id: Optional[str] = None) -> Optional[str]:
        """Execute training pipeline job.

        Args:
            dry_run: If True, only validate without executing
            parent_job_id: Parent job ID for sequential execution

        Returns:
            Job ID if successful, None otherwise
        """
        config_files = ['training']
        config_hash, is_duplicate, default_configs = self._check_execution_state(
            config_files)

        # Check approval
        if not self._prompt_approval(config_hash, is_duplicate, default_configs, dry_run):
            print("❌ Execution cancelled by user")
            return None

        if dry_run:
            print("DRY RUN: Would create training job")
            return "dry-run-training"

        # Trigger Jenkins job
        build_number = self.client.trigger_job(
            self.training_job, {"config_dir": str(self.config_dir)})

        if build_number is not None:
            # Create tracked job
            job_id = self.job_tracker.create_job(
                job_name=self.training_job,
                build_number=build_number,
                config_hash=config_hash,
                job_type='training',
                parent_job_id=parent_job_id
            )

            self._record_execution(config_hash)
            print(
                f"✅ Training job '{self.training_job}' triggered successfully (Job ID: {job_id})")
            return job_id
        else:
            print(f"❌ Failed to trigger training job '{self.training_job}'")
            return None

    def execute_full_pipeline(self, target_config_path: str = "/tmp/parameters.yml", dry_run: bool = False) -> Optional[str]:
        """Execute full pipeline (dataset + training) sequentially.

        Args:
            target_config_path: Path where merged config will be written
            dry_run: If True, only validate without executing

        Returns:
            Pipeline job ID if successful, None otherwise
        """
        print("🚀 Starting full ML pipeline execution")

        # Execute dataset job
        dataset_job_id = self.execute_dataset_job(target_config_path, dry_run)

        if not dataset_job_id:
            print("❌ Pipeline failed at dataset creation step")
            return None

        if dry_run:
            print("DRY RUN: Would continue to training job")
            return "dry-run-pipeline"

        # Create pipeline tracking job
        pipeline_job_id = self.job_tracker.create_job(
            job_name="full-pipeline",
            job_type='full_pipeline'
        )

        print(f"📋 Pipeline Job ID: {pipeline_job_id}")
        print("⏳ Waiting for dataset job to complete before starting training...")

        # Wait for dataset job to complete
        if self._wait_for_job_completion(dataset_job_id):
            print("✅ Dataset job completed successfully")

            # Execute training job
            training_job_id = self.execute_training_job(
                dry_run, parent_job_id=dataset_job_id)

            if training_job_id:
                # Update pipeline job status
                self.job_tracker.update_job_status(
                    pipeline_job_id, JobStatus.RUNNING)

                print("⏳ Waiting for training job to complete...")
                if self._wait_for_job_completion(training_job_id):
                    print("✅ Full pipeline executed successfully")
                    self.job_tracker.update_job_status(
                        pipeline_job_id, JobStatus.SUCCESS)
                    return pipeline_job_id
                else:
                    print("❌ Pipeline failed at training step")
                    self.job_tracker.update_job_status(
                        pipeline_job_id, JobStatus.FAILURE)
                    return None
            else:
                print("❌ Failed to start training job")
                self.job_tracker.update_job_status(
                    pipeline_job_id, JobStatus.FAILURE)
                return None
        else:
            print("❌ Dataset job failed or was cancelled")
            self.job_tracker.update_job_status(
                pipeline_job_id, JobStatus.FAILURE)
            return None

    def _wait_for_job_completion(self, job_id: str, timeout_minutes: int = 60, poll_interval: int = 30) -> bool:
        """Wait for a job to complete.

        Args:
            job_id: Job ID to wait for
            timeout_minutes: Maximum time to wait in minutes
            poll_interval: Polling interval in seconds

        Returns:
            True if job completed successfully, False otherwise
        """
        job = self.job_tracker.get_job(job_id)
        if not job:
            return False

        start_time = time.time()
        timeout_seconds = timeout_minutes * 60

        while time.time() - start_time < timeout_seconds:
            # Get current status from Jenkins
            if job.build_number:
                current_status = self.client.get_build_status(
                    job.job_name, job.build_number)
                self.job_tracker.update_job_status(job_id, current_status)

                if current_status == JobStatus.SUCCESS:
                    return True
                elif current_status in [JobStatus.FAILURE, JobStatus.ABORTED, JobStatus.UNSTABLE]:
                    return False

                # Print status update
                print(f"⏳ Job {job_id} status: {current_status.value}")

            time.sleep(poll_interval)

        print(f"⏰ Timeout waiting for job {job_id}")
        return False

    def get_job_status(self, job_id: str) -> Optional[JobStatus]:
        """Get current status of a job.

        Args:
            job_id: Job ID to check

        Returns:
            Current job status or None if job not found
        """
        job = self.job_tracker.get_job(job_id)
        if not job:
            return None

        # Update status from Jenkins if we have build number
        if job.build_number:
            current_status = self.client.get_build_status(
                job.job_name, job.build_number)
            self.job_tracker.update_job_status(job_id, current_status)
            return current_status

        return job.status

    def cancel_job(self, job_id: str) -> bool:
        """Cancel a job.

        Args:
            job_id: Job ID to cancel

        Returns:
            True if cancellation was successful
        """
        job = self.job_tracker.get_job(job_id)
        if not job:
            print(f"❌ Job {job_id} not found")
            return False

        if job.build_number:
            success = self.client.cancel_build(job.job_name, job.build_number)
            if success:
                self.job_tracker.update_job_status(job_id, JobStatus.ABORTED)
                print(f"✅ Job {job_id} cancelled successfully")
                return True
            else:
                print(f"❌ Failed to cancel job {job_id}")
                return False
        else:
            print(f"❌ Cannot cancel job {job_id} - no build number available")
            return False

    def list_jobs(self) -> List:
        """List all tracked jobs."""
        return self.job_tracker.get_all_jobs()

    def init_defaults(self) -> None:
        """Initialize default configuration hashes."""
        state = self._load_state()
        defaults = {}

        for config_file in ['query', 'sp', 'training']:
            file_path = self.config_dir / f"{config_file}_config.yml"
            if file_path.exists():
                with file_path.open('r') as f:
                    defaults[config_file] = hashlib.sha256(
                        f.read().encode()).hexdigest()

        state['defaults'] = defaults
        self._save_state(state)
        print(f"✅ Initialized default configs: {', '.join(defaults.keys())}")

    def close(self) -> None:
        """Close the service."""
        self.client.close()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
