import sys
from typing import Optional

import click

from jenkins_job_manager import __version__


def handle_error(error: Exception) -> None:
    """Handle and display errors."""
    print(f"Error: {error}")
    sys.exit(1)


@click.group()
@click.version_option(version=__version__, prog_name="jjm")
def app() -> None:
    """Jenkins Job Manager - Simple Jenkins job triggering tool.

    Environment Variables:\n
        JENKINS_USER: Jenkins username for authentication\n
        JENKINS_API_TOKEN: Jenkins API token for authentication\n
    """
    pass


@app.command()
@click.option("--jenkins-url", help="Jenkins server URL (if not provided, uses config)")
@click.option("--dataset-only", is_flag=True, help="Execute only dataset creation job")
@click.option("--dry-run", "-n", is_flag=True, help="Show what would be executed")
@click.option("--config-dir", "-c", default="conf", help="Config directory (default: conf)")
@click.option("--target-path", default="/tmp/parameters.yml", help="Target config path")
def trigger(jenkins_url: Optional[str], dataset_only: bool, dry_run: bool, config_dir: str, target_path: str) -> None:
    """Trigger Jenkins jobs for ML pipeline execution."""
    try:
        from jenkins_job_manager.service.jenkins_service import JenkinsService

        with JenkinsService(jenkins_url, config_dir) as service:
            if dataset_only:
                success = service.execute_dataset_job(target_path, dry_run)
            else:
                success = service.execute_full_pipeline(target_path, dry_run)

            if not success:
                sys.exit(1)

    except Exception as e:
        handle_error(e)


@app.command()
@click.option("--config-dir", "-c", default="conf", help="Config directory")
def init_defaults(config_dir: str) -> None:
    """Initialize current configurations as default baseline."""
    try:
        from jenkins_job_manager.service.jenkins_service import JenkinsService

        service = JenkinsService(config_dir=config_dir)
        service.init_defaults()

    except Exception as e:
        handle_error(e)


@app.group()
def config() -> None:
    """Manage JJM configuration."""
    pass


@config.command("show")
def config_show() -> None:
    """Show current configuration."""
    try:
        from jenkins_job_manager.config.jjm_config import load_config, get_config_file

        config = load_config()
        config_file = get_config_file()

        print(f"Configuration file: {config_file}")
        print(f"Jenkins URL: {config.get('jenkins_url', 'Not set')}")
        print(f"Dataset job: {config.get('dataset_job', 'dataset-creation-job')}")
        print(f"Training job: {config.get('training_job', 'training-pipeline-job')}")

    except Exception as e:
        handle_error(e)


@config.command("set")
@click.argument("key")
@click.argument("value")
def config_set(key: str, value: str) -> None:
    """Set a configuration value.

    Examples:
        jjm config set jenkins_url https://jenkins.example.com
        jjm config set dataset_job my-dataset-job
    """
    try:
        from jenkins_job_manager.config.jjm_config import load_config, save_config

        config = load_config()
        config[key] = value
        save_config(config)

        print(f"✅ Set {key} = {value}")

    except Exception as e:
        handle_error(e)


@app.command()
def version() -> None:
    """Show version information."""
    print(f"Jenkins Job Manager (jjm) version {__version__}")


if __name__ == "__main__":
    app()
