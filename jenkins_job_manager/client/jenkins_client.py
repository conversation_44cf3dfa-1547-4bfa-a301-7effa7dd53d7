import os
from typing import Dict, Optional
from urllib.parse import urljoin

import httpx


class JenkinsClient:
    """Simple Jenkins REST API client."""

    def __init__(self, base_url: str, username: Optional[str] = None, api_token: Optional[str] = None):
        """Initialize Jenkins client.

        Args:
            base_url: Jenkins server URL
            username: <PERSON> username (or from JENKINS_USER env var)
            api_token: Jenkins API token (or from JENKINS_API_TOKEN env var)
        """
        self.base_url = base_url.rstrip('/') + '/'

        # Get credentials from parameters or environment
        self.username = username or os.getenv('JENKINS_USER')
        self.api_token = api_token or os.getenv('JENKINS_API_TOKEN')

        if not self.username or not self.api_token:
            raise ValueError("Jenkins username and API token are required")

        # Initialize HTTP client
        self._client = httpx.Client(
            auth=(self.username, self.api_token),
            timeout=30.0,
            follow_redirects=True
        )

        # Get CSRF crumb if needed
        self._crumb = self._get_crumb()

    def _get_crumb(self) -> Optional[Dict[str, str]]:
        """Get Jenkins CSRF crumb if enabled."""
        try:
            response = self._client.get(urljoin(self.base_url, "crumbIssuer/api/json"))
            if response.status_code == 200:
                crumb_data = response.json()
                return {crumb_data["crumbRequestField"]: crumb_data["crumb"]}
        except Exception:
            pass  # Crumb not required or not available
        return None

    def trigger_job(self, job_name: str, parameters: Optional[Dict] = None) -> bool:
        """Trigger a Jenkins job.

        Args:
            job_name: Name of the Jenkins job
            parameters: Optional job parameters

        Returns:
            True if job was triggered successfully
        """
        if parameters:
            endpoint = f"job/{job_name}/buildWithParameters"
            data = parameters
        else:
            endpoint = f"job/{job_name}/build"
            data = {}

        headers = {}
        if self._crumb:
            headers.update(self._crumb)

        try:
            response = self._client.post(
                urljoin(self.base_url, endpoint),
                data=data,
                headers=headers
            )
            return response.status_code in [200, 201]
        except Exception:
            return False

    def job_exists(self, job_name: str) -> bool:
        """Check if a Jenkins job exists."""
        try:
            response = self._client.get(urljoin(self.base_url, f"job/{job_name}/api/json"))
            return response.status_code == 200
        except Exception:
            return False

    def close(self) -> None:
        """Close the HTTP client."""
        self._client.close()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
