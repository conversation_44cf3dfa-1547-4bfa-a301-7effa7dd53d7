import hashlib
import yaml
from pathlib import Path
from typing import Dict, List, Any, Optional


class ConfigManager:
    """Handles configuration file merging and writing operations."""
    
    def __init__(self, config_dir: Path):
        """Initialize config manager.
        
        Args:
            config_dir: Directory containing configuration files
        """
        self.config_dir = Path(config_dir)
    
    def merge_configs(self, config_files: List[str]) -> Dict[str, Any]:
        """Merge multiple configuration files.
        
        Args:
            config_files: List of config file names (without _config.yml suffix)
            
        Returns:
            Merged configuration dictionary
        """
        merged = {}
        
        for config_file in config_files:
            file_path = self.config_dir / f"{config_file}_config.yml"
            if file_path.exists():
                try:
                    with file_path.open('r') as f:
                        config_data = yaml.safe_load(f)
                        if config_data:
                            merged.update(config_data)
                except yaml.YAMLError as e:
                    raise ValueError(f"Error parsing {file_path}: {e}")
        
        return merged
    
    def write_config(self, config_data: Dict[str, Any], target_path: str) -> None:
        """Write configuration data to a file.
        
        Args:
            config_data: Configuration dictionary to write
            target_path: Target file path
        """
        target = Path(target_path)
        target.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            with target.open('w') as f:
                yaml.dump(config_data, f, default_flow_style=False, indent=2)
        except Exception as e:
            raise ValueError(f"Error writing config to {target_path}: {e}")
    
    def get_config_hash(self, config_files: List[str]) -> str:
        """Get hash of configuration files for duplicate detection.
        
        Args:
            config_files: List of config file names (without _config.yml suffix)
            
        Returns:
            SHA256 hash (first 12 characters)
        """
        content = ""
        for config_file in config_files:
            file_path = self.config_dir / f"{config_file}_config.yml"
            if file_path.exists():
                try:
                    with file_path.open('r') as f:
                        content += f.read()
                except Exception:
                    # If we can't read a file, include its name in the hash
                    content += f"ERROR_READING_{config_file}"
        
        return hashlib.sha256(content.encode()).hexdigest()[:12]
    
    def config_exists(self, config_file: str) -> bool:
        """Check if a configuration file exists.
        
        Args:
            config_file: Config file name (without _config.yml suffix)
            
        Returns:
            True if file exists
        """
        file_path = self.config_dir / f"{config_file}_config.yml"
        return file_path.exists()
    
    def get_config_file_hash(self, config_file: str) -> Optional[str]:
        """Get hash of a single configuration file.
        
        Args:
            config_file: Config file name (without _config.yml suffix)
            
        Returns:
            SHA256 hash of the file content, or None if file doesn't exist
        """
        file_path = self.config_dir / f"{config_file}_config.yml"
        if not file_path.exists():
            return None
        
        try:
            with file_path.open('r') as f:
                content = f.read()
            return hashlib.sha256(content.encode()).hexdigest()
        except Exception:
            return None
    
    def validate_config_files(self, config_files: List[str]) -> List[str]:
        """Validate that configuration files exist and are readable.
        
        Args:
            config_files: List of config file names to validate
            
        Returns:
            List of missing or invalid config files
        """
        missing_files = []
        
        for config_file in config_files:
            file_path = self.config_dir / f"{config_file}_config.yml"
            if not file_path.exists():
                missing_files.append(f"{config_file}_config.yml (missing)")
                continue
            
            try:
                with file_path.open('r') as f:
                    yaml.safe_load(f)
            except yaml.YAMLError:
                missing_files.append(f"{config_file}_config.yml (invalid YAML)")
            except Exception:
                missing_files.append(f"{config_file}_config.yml (unreadable)")
        
        return missing_files
    
    def merge_and_write(self, config_files: List[str], target_path: str) -> str:
        """Merge configuration files and write to target path.
        
        Args:
            config_files: List of config file names to merge
            target_path: Target file path to write merged config
            
        Returns:
            Configuration hash of the merged files
        """
        # Validate config files first
        invalid_files = self.validate_config_files(config_files)
        if invalid_files:
            raise ValueError(f"Invalid config files: {', '.join(invalid_files)}")
        
        # Get hash before merging
        config_hash = self.get_config_hash(config_files)
        
        # Merge and write
        merged_config = self.merge_configs(config_files)
        self.write_config(merged_config, target_path)
        
        return config_hash
