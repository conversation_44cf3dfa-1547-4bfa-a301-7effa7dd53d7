import hashlib
import json
import time
from pathlib import Path
from typing import Dict, List, Optional

import yaml

from jenkins_job_manager.client.jenkins_client import Jenkins<PERSON>lient
from jenkins_job_manager.config.jjm_config import load_config


class JenkinsService:
    """Simple Jenkins job management service."""
    
    def __init__(self, jenkins_url: Optional[str] = None, config_dir: str = "conf"):
        """Initialize Jenkins service.
        
        Args:
            jenkins_url: Jenkins server URL (if None, loads from config)
            config_dir: Directory containing config files
        """
        self.config_dir = Path(config_dir)
        
        # Get Jenkins URL and job names from config
        config = load_config()
        self.jenkins_url = jenkins_url or config.get('jenkins_url', 'http://localhost:8080')
        self.dataset_job, self.training_job = config.get('dataset_job', 'dataset-creation-job'), config.get('training_job', 'training-pipeline-job')
        
        # Initialize Jenkins client
        self.client = JenkinsClient(self.jenkins_url)
        
        # State tracking
        self.state_file = self.config_dir / 'jjm_state.json'
    
    def _load_state(self) -> Dict:
        """Load state from file."""
        if self.state_file.exists():
            with self.state_file.open('r') as f:
                return json.load(f)
        return {}
    
    def _save_state(self, state: Dict) -> None:
        """Save state to file."""
        with self.state_file.open('w') as f:
            json.dump(state, f, indent=2)
    
    def _get_config_hash(self, config_files: List[str]) -> str:
        """Get hash of config files for duplicate detection."""
        content = ""
        for config_file in config_files:
            file_path = self.config_dir / f"{config_file}_config.yml"
            if file_path.exists():
                with file_path.open('r') as f:
                    content += f.read()
        return hashlib.sha256(content.encode()).hexdigest()[:12]
    
    def _is_default_config(self, config_file: str) -> bool:
        """Check if config file is still using default values."""
        state = self._load_state()
        defaults = state.get('defaults', {})
        
        file_path = self.config_dir / f"{config_file}_config.yml"
        if not file_path.exists():
            return True
        
        with file_path.open('r') as f:
            current_hash = hashlib.sha256(f.read().encode()).hexdigest()
        
        return current_hash == defaults.get(config_file)
    
    def _check_execution_state(self, config_files: List[str]) -> tuple[str, bool, List[str]]:
        """Check execution state and return hash, duplicate status, and default configs."""
        config_hash = self._get_config_hash(config_files)
        default_configs = [cf for cf in config_files if self._is_default_config(cf)]
        
        state = self._load_state()
        executions = state.get('executions', {})
        is_duplicate = config_hash in executions
        
        return config_hash, is_duplicate, default_configs
    
    def _record_execution(self, config_hash: str) -> None:
        """Record execution in state."""
        state = self._load_state()
        executions = state.setdefault('executions', {})
        
        if config_hash in executions:
            executions[config_hash]['count'] += 1
        else:
            executions[config_hash] = {'count': 1, 'first_run': time.time()}
        
        executions[config_hash]['last_run'] = time.time()
        self._save_state(state)
    
    def _merge_configs(self, config_files: List[str]) -> Dict:
        """Merge configuration files."""
        merged = {}
        
        for config_file in config_files:
            file_path = self.config_dir / f"{config_file}_config.yml"
            if file_path.exists():
                with file_path.open('r') as f:
                    config_data = yaml.safe_load(f)
                    if config_data:
                        merged.update(config_data)
        
        return merged
    
    def _write_merged_config(self, merged_config: Dict, target_path: str) -> None:
        """Write merged config to target path."""
        target = Path(target_path)
        target.parent.mkdir(parents=True, exist_ok=True)
        
        with target.open('w') as f:
            yaml.dump(merged_config, f, default_flow_style=False, indent=2)
    
    def _prompt_approval(self, config_hash: str, is_duplicate: bool, default_configs: List[str], dry_run: bool = False) -> bool:
        """Prompt user for approval if needed."""
        if dry_run:
            print(f"DRY RUN: Would execute with config hash {config_hash}")
            if default_configs:
                print(f"⚠️  Using default configs: {', '.join(default_configs)}")
            if is_duplicate:
                print("🔄 This configuration has been executed before")
            return True
        
        if default_configs or is_duplicate:
            print(f"\n📋 Configuration Summary:")
            print(f"Config hash: {config_hash}")
            
            if default_configs:
                print(f"⚠️  Using default configs: {', '.join(default_configs)}")
            
            if is_duplicate:
                print("🔄 This configuration has been executed before")
            
            response = input("\nProceed? [y/N]: ").strip().lower()
            return response in ['y', 'yes']
        
        return True
    
    def execute_dataset_job(self, target_config_path: str = "/tmp/parameters.yml", dry_run: bool = False) -> bool:
        """Execute dataset creation job.
        
        Args:
            target_config_path: Path where merged config will be written
            dry_run: If True, only validate without executing
            
        Returns:
            True if successful
        """
        config_files = ['query', 'sp']
        config_hash, is_duplicate, default_configs = self._check_execution_state(config_files)
        
        # Check approval
        if not self._prompt_approval(config_hash, is_duplicate, default_configs, dry_run):
            print("❌ Execution cancelled by user")
            return False
        
        if dry_run:
            return True
        
        # Merge configs and write to target
        merged_config = self._merge_configs(config_files)
        self._write_merged_config(merged_config, target_config_path)
        
        # Trigger Jenkins job
        success = self.client.trigger_job(self.dataset_job, {"config_path": target_config_path})
        
        if success:
            self._record_execution(config_hash)
            print(f"✅ Dataset job '{self.dataset_job}' triggered successfully")
        else:
            print(f"❌ Failed to trigger dataset job '{self.dataset_job}'")
        
        return success
    
    def execute_training_job(self, dry_run: bool = False) -> bool:
        """Execute training pipeline job.
        
        Args:
            dry_run: If True, only validate without executing
            
        Returns:
            True if successful
        """
        config_files = ['training']
        config_hash, is_duplicate, default_configs = self._check_execution_state(config_files)
        
        # Check approval
        if not self._prompt_approval(config_hash, is_duplicate, default_configs, dry_run):
            print("❌ Execution cancelled by user")
            return False
        
        if dry_run:
            return True
        
        # Trigger Jenkins job
        success = self.client.trigger_job(self.training_job, {"config_dir": str(self.config_dir)})
        
        if success:
            self._record_execution(config_hash)
            print(f"✅ Training job '{self.training_job}' triggered successfully")
        else:
            print(f"❌ Failed to trigger training job '{self.training_job}'")
        
        return success
    
    def execute_full_pipeline(self, target_config_path: str = "/tmp/parameters.yml", dry_run: bool = False) -> bool:
        """Execute full pipeline (dataset + training).
        
        Args:
            target_config_path: Path where merged config will be written
            dry_run: If True, only validate without executing
            
        Returns:
            True if both jobs successful
        """
        print("🚀 Starting full ML pipeline execution")
        
        # Execute dataset job
        dataset_success = self.execute_dataset_job(target_config_path, dry_run)
        
        if not dataset_success:
            print("❌ Pipeline failed at dataset creation step")
            return False
        
        if dry_run:
            print("DRY RUN: Would continue to training job")
            return True
        
        # Wait a bit for dataset job to start
        print("⏳ Waiting before starting training job...")
        time.sleep(5)
        
        # Execute training job
        training_success = self.execute_training_job(dry_run)
        
        if training_success:
            print("✅ Full pipeline executed successfully")
        else:
            print("❌ Pipeline failed at training step")
        
        return training_success
    
    def init_defaults(self) -> None:
        """Initialize default configuration hashes."""
        state = self._load_state()
        defaults = {}
        
        for config_file in ['query', 'sp', 'training']:
            file_path = self.config_dir / f"{config_file}_config.yml"
            if file_path.exists():
                with file_path.open('r') as f:
                    defaults[config_file] = hashlib.sha256(f.read().encode()).hexdigest()
        
        state['defaults'] = defaults
        self._save_state(state)
        print(f"✅ Initialized default configs: {', '.join(defaults.keys())}")
    
    def close(self) -> None:
        """Close the service."""
        self.client.close()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
