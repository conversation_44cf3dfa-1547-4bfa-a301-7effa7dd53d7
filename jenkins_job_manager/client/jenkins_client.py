import os
from typing import Dict, Optional, Any, List
from urllib.parse import urljoin
from enum import Enum

import httpx


class JobStatus(Enum):
    """Jenkins job status enumeration."""
    UNKNOWN = "unknown"
    QUEUED = "queued"
    RUNNING = "running"
    SUCCESS = "success"
    FAILURE = "failure"
    ABORTED = "aborted"
    UNSTABLE = "unstable"


class JenkinsClient:
    """Simple Jenkins REST API client."""

    def __init__(self, base_url: str, username: Optional[str] = None, api_token: Optional[str] = None):
        """Initialize Jenkins client.

        Args:
            base_url: Jenkins server URL
            username: <PERSON> username (or from JENKINS_USER env var)
            api_token: Jenkins API token (or from JENKINS_API_TOKEN env var)
        """
        self.base_url = base_url.rstrip('/') + '/'

        # Get credentials from parameters or environment
        self.username = username or os.getenv('JENKINS_USER')
        self.api_token = api_token or os.getenv('JENKINS_API_TOKEN')

        if not self.username or not self.api_token:
            raise ValueError("Jenkins username and API token are required")

        # Initialize HTTP client
        self._client = httpx.Client(
            auth=(self.username, self.api_token),
            timeout=30.0,
            follow_redirects=True
        )

        # Get CSRF crumb if needed
        self._crumb = self._get_crumb()

    def _get_crumb(self) -> Optional[Dict[str, str]]:
        """Get Jenkins CSRF crumb if enabled."""
        try:
            response = self._client.get(
                urljoin(self.base_url, "crumbIssuer/api/json"))
            if response.status_code == 200:
                crumb_data = response.json()
                return {crumb_data["crumbRequestField"]: crumb_data["crumb"]}
        except Exception:
            pass  # Crumb not required or not available
        return None

    def trigger_job(self, job_name: str, parameters: Optional[Dict] = None) -> Optional[int]:
        """Trigger a Jenkins job.

        Args:
            job_name: Name of the Jenkins job
            parameters: Optional job parameters

        Returns:
            Build number if job was triggered successfully, None otherwise
        """
        if parameters:
            endpoint = f"job/{job_name}/buildWithParameters"
            data = parameters
        else:
            endpoint = f"job/{job_name}/build"
            data = {}

        headers = {}
        if self._crumb:
            headers.update(self._crumb)

        try:
            response = self._client.post(
                urljoin(self.base_url, endpoint),
                data=data,
                headers=headers
            )
            if response.status_code in [200, 201]:
                # Try to get the build number from the queue
                return self._get_next_build_number(job_name)
            return None
        except Exception:
            return None

    def job_exists(self, job_name: str) -> bool:
        """Check if a Jenkins job exists."""
        try:
            response = self._client.get(
                urljoin(self.base_url, f"job/{job_name}/api/json"))
            return response.status_code == 200
        except Exception:
            return False

    def _get_next_build_number(self, job_name: str) -> Optional[int]:
        """Get the next build number for a job."""
        try:
            response = self._client.get(
                urljoin(self.base_url, f"job/{job_name}/api/json"))
            if response.status_code == 200:
                job_data = response.json()
                return job_data.get('nextBuildNumber')
            return None
        except Exception:
            return None

    def get_build_status(self, job_name: str, build_number: int) -> JobStatus:
        """Get the status of a specific build.

        Args:
            job_name: Name of the Jenkins job
            build_number: Build number

        Returns:
            JobStatus enum value
        """
        try:
            response = self._client.get(
                urljoin(self.base_url,
                        f"job/{job_name}/{build_number}/api/json")
            )
            if response.status_code == 200:
                build_data = response.json()
                if build_data.get('building', False):
                    return JobStatus.RUNNING

                result = build_data.get('result', '').upper()
                if result == 'SUCCESS':
                    return JobStatus.SUCCESS
                elif result == 'FAILURE':
                    return JobStatus.FAILURE
                elif result == 'ABORTED':
                    return JobStatus.ABORTED
                elif result == 'UNSTABLE':
                    return JobStatus.UNSTABLE
                else:
                    return JobStatus.UNKNOWN
            elif response.status_code == 404:
                # Build might be in queue
                return self._check_queue_status(job_name, build_number)
            return JobStatus.UNKNOWN
        except Exception:
            return JobStatus.UNKNOWN

    def _check_queue_status(self, job_name: str, build_number: int) -> JobStatus:
        """Check if a build is in the queue."""
        try:
            response = self._client.get(
                urljoin(self.base_url, "queue/api/json"))
            if response.status_code == 200:
                queue_data = response.json()
                for item in queue_data.get('items', []):
                    task = item.get('task', {})
                    if task.get('name') == job_name:
                        return JobStatus.QUEUED
            return JobStatus.UNKNOWN
        except Exception:
            return JobStatus.UNKNOWN

    def cancel_build(self, job_name: str, build_number: int) -> bool:
        """Cancel a running or queued build.

        Args:
            job_name: Name of the Jenkins job
            build_number: Build number to cancel

        Returns:
            True if cancellation was successful
        """
        headers = {}
        if self._crumb:
            headers.update(self._crumb)

        try:
            # Try to stop a running build
            response = self._client.post(
                urljoin(self.base_url, f"job/{job_name}/{build_number}/stop"),
                headers=headers
            )
            if response.status_code in [200, 201, 302]:
                return True

            # If that fails, try to cancel from queue
            return self._cancel_from_queue(job_name)
        except Exception:
            return False

    def _cancel_from_queue(self, job_name: str) -> bool:
        """Cancel a job from the build queue."""
        try:
            response = self._client.get(
                urljoin(self.base_url, "queue/api/json"))
            if response.status_code == 200:
                queue_data = response.json()
                for item in queue_data.get('items', []):
                    task = item.get('task', {})
                    if task.get('name') == job_name:
                        queue_id = item.get('id')
                        if queue_id:
                            headers = {}
                            if self._crumb:
                                headers.update(self._crumb)

                            cancel_response = self._client.post(
                                urljoin(self.base_url,
                                        f"queue/cancelItem?id={queue_id}"),
                                headers=headers
                            )
                            return cancel_response.status_code in [200, 201, 302]
            return False
        except Exception:
            return False

    def get_build_info(self, job_name: str, build_number: int) -> Optional[Dict[str, Any]]:
        """Get detailed build information.

        Args:
            job_name: Name of the Jenkins job
            build_number: Build number

        Returns:
            Build information dictionary or None if not found
        """
        try:
            response = self._client.get(
                urljoin(self.base_url,
                        f"job/{job_name}/{build_number}/api/json")
            )
            if response.status_code == 200:
                return response.json()
            return None
        except Exception:
            return None

    def close(self) -> None:
        """Close the HTTP client."""
        self._client.close()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
