"""Tests for jenkins_job_manager.client.jenkins_client module."""

import os
import pytest
from unittest.mock import Mock, patch

from jenkins_job_manager.client.jenkins_client import JenkinsClient, JobStatus


class TestJenkinsClient:
    """Test cases for JenkinsClient class."""

    @patch.dict(os.environ, {'JENKINS_USER': 'test_user', 'JENKINS_API_TOKEN': 'test_token'})
    def test_client_initialization(self) -> None:
        """Test Jenkins client initialization."""
        client = JenkinsClient("https://jenkins.example.com")

        assert client.base_url == "https://jenkins.example.com/"
        assert client.username == "test_user"
        assert client.api_token == "test_token"

    def test_client_initialization_with_credentials(self) -> None:
        """Test Jenkins client initialization with explicit credentials."""
        client = JenkinsClient("https://jenkins.example.com", "user", "token")

        assert client.username == "user"
        assert client.api_token == "token"

    def test_client_initialization_missing_credentials(self) -> None:
        """Test Jenkins client initialization without credentials."""
        with patch.dict(os.environ, {}, clear=True):
            with pytest.raises(ValueError, match="<PERSON> username and API token are required"):
                JenkinsClient("https://jenkins.example.com")

    def test_url_normalization(self) -> None:
        """Test URL normalization (adding trailing slash)."""
        with patch.dict(os.environ, {'JENKINS_USER': 'user', 'JENKINS_API_TOKEN': 'token'}):
            client = JenkinsClient("https://jenkins.example.com")
            assert client.base_url == "https://jenkins.example.com/"

            client = JenkinsClient("https://jenkins.example.com/")
            assert client.base_url == "https://jenkins.example.com/"

    @patch.dict(os.environ, {'JENKINS_USER': 'user', 'JENKINS_API_TOKEN': 'token'})
    def test_trigger_job_success(self) -> None:
        """Test successful job triggering."""
        client = JenkinsClient("https://jenkins.example.com")

        # Mock successful response and next build number
        mock_response = Mock()
        mock_response.status_code = 201
        client._client.post = Mock(return_value=mock_response)
        client._get_next_build_number = Mock(return_value=123)

        result = client.trigger_job("test-job", {"param": "value"})
        assert result == 123  # Should return build number

    @patch.dict(os.environ, {'JENKINS_USER': 'user', 'JENKINS_API_TOKEN': 'token'})
    def test_trigger_job_without_parameters(self) -> None:
        """Test job triggering without parameters."""
        client = JenkinsClient("https://jenkins.example.com")

        # Mock successful response and next build number
        mock_response = Mock()
        mock_response.status_code = 200
        client._client.post = Mock(return_value=mock_response)
        client._get_next_build_number = Mock(return_value=456)

        result = client.trigger_job("test-job")
        assert result == 456  # Should return build number

    @patch.dict(os.environ, {'JENKINS_USER': 'user', 'JENKINS_API_TOKEN': 'token'})
    def test_trigger_job_failure(self) -> None:
        """Test job triggering failure."""
        client = JenkinsClient("https://jenkins.example.com")

        # Mock failed response
        mock_response = Mock()
        mock_response.status_code = 404
        client._client.post = Mock(return_value=mock_response)

        result = client.trigger_job("test-job")
        assert result is None  # Should return None on failure

    @patch.dict(os.environ, {'JENKINS_USER': 'user', 'JENKINS_API_TOKEN': 'token'})
    def test_job_exists_true(self) -> None:
        """Test job existence check - job exists."""
        client = JenkinsClient("https://jenkins.example.com")

        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 200
        client._client.get = Mock(return_value=mock_response)

        result = client.job_exists("test-job")
        assert result is True

    @patch.dict(os.environ, {'JENKINS_USER': 'user', 'JENKINS_API_TOKEN': 'token'})
    def test_job_exists_false(self) -> None:
        """Test job existence check - job doesn't exist."""
        client = JenkinsClient("https://jenkins.example.com")

        # Mock 404 response
        mock_response = Mock()
        mock_response.status_code = 404
        client._client.get = Mock(return_value=mock_response)

        result = client.job_exists("test-job")
        assert result is False

    @patch.dict(os.environ, {'JENKINS_USER': 'user', 'JENKINS_API_TOKEN': 'token'})
    def test_context_manager(self) -> None:
        """Test Jenkins client as context manager."""
        with JenkinsClient("https://jenkins.example.com") as client:
            assert isinstance(client, JenkinsClient)

        # Client should be closed after context exit
        # Note: We can't easily test this without mocking the close method

    @patch.dict(os.environ, {'JENKINS_USER': 'user', 'JENKINS_API_TOKEN': 'token'})
    def test_get_build_status_running(self) -> None:
        """Test getting build status for running job."""
        client = JenkinsClient("https://jenkins.example.com")

        # Mock response for running build
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {'building': True}
        client._client.get = Mock(return_value=mock_response)

        status = client.get_build_status("test-job", 123)
        assert status == JobStatus.RUNNING

    @patch.dict(os.environ, {'JENKINS_USER': 'user', 'JENKINS_API_TOKEN': 'token'})
    def test_get_build_status_success(self) -> None:
        """Test getting build status for successful job."""
        client = JenkinsClient("https://jenkins.example.com")

        # Mock response for successful build
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'building': False, 'result': 'SUCCESS'}
        client._client.get = Mock(return_value=mock_response)

        status = client.get_build_status("test-job", 123)
        assert status == JobStatus.SUCCESS

    @patch.dict(os.environ, {'JENKINS_USER': 'user', 'JENKINS_API_TOKEN': 'token'})
    def test_cancel_build_success(self) -> None:
        """Test successful build cancellation."""
        client = JenkinsClient("https://jenkins.example.com")

        # Mock successful cancellation response
        mock_response = Mock()
        mock_response.status_code = 200
        client._client.post = Mock(return_value=mock_response)

        result = client.cancel_build("test-job", 123)
        assert result is True

    @patch.dict(os.environ, {'JENKINS_USER': 'user', 'JENKINS_API_TOKEN': 'token'})
    def test_get_build_info(self) -> None:
        """Test getting build information."""
        client = JenkinsClient("https://jenkins.example.com")

        # Mock build info response
        build_info = {
            'id': '123',
            'building': False,
            'result': 'SUCCESS',
            'duration': 30000
        }
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = build_info
        client._client.get = Mock(return_value=mock_response)

        info = client.get_build_info("test-job", 123)
        assert info == build_info
