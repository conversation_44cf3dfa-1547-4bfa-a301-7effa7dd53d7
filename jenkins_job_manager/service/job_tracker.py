import json
import time
import uuid
from dataclasses import dataclass, asdict
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime

from jenkins_job_manager.client.jenkins_client import JobStatus


@dataclass
class TrackedJob:
    """Represents a tracked <PERSON> job."""
    job_id: str
    job_name: str
    build_number: Optional[int]
    status: JobStatus
    created_at: float
    updated_at: float
    config_hash: Optional[str] = None
    job_type: Optional[str] = None  # 'dataset', 'training', 'full_pipeline'
    parent_job_id: Optional[str] = None  # For sequential jobs
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        data = asdict(self)
        data['status'] = self.status.value
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TrackedJob':
        """Create from dictionary."""
        data['status'] = JobStatus(data['status'])
        return cls(**data)
    
    def is_active(self) -> bool:
        """Check if job is still active (running or queued)."""
        return self.status in [JobStatus.RUNNING, JobStatus.QUEUED]
    
    def is_completed(self) -> bool:
        """Check if job is completed (success, failure, or aborted)."""
        return self.status in [JobStatus.SUCCESS, JobStatus.FAILURE, JobStatus.ABORTED, JobStatus.UNSTABLE]


class JobTracker:
    """Manages job tracking and state persistence."""
    
    def __init__(self, state_dir: Path):
        """Initialize job tracker.
        
        Args:
            state_dir: Directory to store state files
        """
        self.state_dir = Path(state_dir)
        self.state_dir.mkdir(parents=True, exist_ok=True)
        self.jobs_file = self.state_dir / 'tracked_jobs.json'
        self._jobs: Dict[str, TrackedJob] = {}
        self._load_jobs()
    
    def _load_jobs(self) -> None:
        """Load jobs from state file."""
        if self.jobs_file.exists():
            try:
                with self.jobs_file.open('r') as f:
                    jobs_data = json.load(f)
                    self._jobs = {
                        job_id: TrackedJob.from_dict(job_data)
                        for job_id, job_data in jobs_data.items()
                    }
            except (json.JSONDecodeError, KeyError, ValueError):
                # If file is corrupted, start fresh
                self._jobs = {}
    
    def _save_jobs(self) -> None:
        """Save jobs to state file."""
        jobs_data = {
            job_id: job.to_dict()
            for job_id, job in self._jobs.items()
        }
        with self.jobs_file.open('w') as f:
            json.dump(jobs_data, f, indent=2)
    
    def create_job(
        self,
        job_name: str,
        build_number: Optional[int] = None,
        config_hash: Optional[str] = None,
        job_type: Optional[str] = None,
        parent_job_id: Optional[str] = None
    ) -> str:
        """Create a new tracked job.
        
        Args:
            job_name: Jenkins job name
            build_number: Build number (if known)
            config_hash: Configuration hash
            job_type: Type of job ('dataset', 'training', 'full_pipeline')
            parent_job_id: Parent job ID for sequential jobs
            
        Returns:
            Unique job ID
        """
        job_id = str(uuid.uuid4())[:8]  # Short UUID for user convenience
        current_time = time.time()
        
        job = TrackedJob(
            job_id=job_id,
            job_name=job_name,
            build_number=build_number,
            status=JobStatus.QUEUED if build_number else JobStatus.UNKNOWN,
            created_at=current_time,
            updated_at=current_time,
            config_hash=config_hash,
            job_type=job_type,
            parent_job_id=parent_job_id
        )
        
        self._jobs[job_id] = job
        self._save_jobs()
        return job_id
    
    def update_job_status(self, job_id: str, status: JobStatus, build_number: Optional[int] = None) -> bool:
        """Update job status.
        
        Args:
            job_id: Job ID to update
            status: New status
            build_number: Build number (if available)
            
        Returns:
            True if job was updated
        """
        if job_id not in self._jobs:
            return False
        
        job = self._jobs[job_id]
        job.status = status
        job.updated_at = time.time()
        
        if build_number is not None:
            job.build_number = build_number
        
        self._save_jobs()
        return True
    
    def get_job(self, job_id: str) -> Optional[TrackedJob]:
        """Get a tracked job by ID."""
        return self._jobs.get(job_id)
    
    def get_all_jobs(self) -> List[TrackedJob]:
        """Get all tracked jobs."""
        return list(self._jobs.values())
    
    def get_active_jobs(self) -> List[TrackedJob]:
        """Get all active (running or queued) jobs."""
        return [job for job in self._jobs.values() if job.is_active()]
    
    def get_jobs_by_type(self, job_type: str) -> List[TrackedJob]:
        """Get jobs by type."""
        return [job for job in self._jobs.values() if job.job_type == job_type]
    
    def remove_job(self, job_id: str) -> bool:
        """Remove a tracked job.
        
        Args:
            job_id: Job ID to remove
            
        Returns:
            True if job was removed
        """
        if job_id in self._jobs:
            del self._jobs[job_id]
            self._save_jobs()
            return True
        return False
    
    def cleanup_old_jobs(self, max_age_days: int = 30) -> int:
        """Remove jobs older than specified days.
        
        Args:
            max_age_days: Maximum age in days
            
        Returns:
            Number of jobs removed
        """
        cutoff_time = time.time() - (max_age_days * 24 * 60 * 60)
        old_jobs = [
            job_id for job_id, job in self._jobs.items()
            if job.updated_at < cutoff_time and job.is_completed()
        ]
        
        for job_id in old_jobs:
            del self._jobs[job_id]
        
        if old_jobs:
            self._save_jobs()
        
        return len(old_jobs)
    
    def get_job_summary(self) -> Dict[str, int]:
        """Get summary of job statuses."""
        summary = {}
        for job in self._jobs.values():
            status_name = job.status.value
            summary[status_name] = summary.get(status_name, 0) + 1
        return summary
